import React, { useState } from "react";
import { VStack } from "@/components/ui/vstack";
import { SafeAreaView } from "react-native-safe-area-context";
import ClassesHeader from "@/components/screens/classes/classes-header";
import HorizontalDatePicker from "@/components/shared/horizontal-date-picker";
import ClassesTabs from "@/components/screens/classes/classes-tabs";
import ClassCard, {
  ClassCardSkeleton,
} from "@/components/screens/classes/class-card";

import { SearchInput } from "@/components/screens/classes/classes-header/search";
import { FlatList } from "react-native";
import { useClassesQuery } from "@/data/screens/location/queries/useClassesQuery";

export const Classes = () => {
  const [selectedTab, setSelectedTab] = useState<"classes" | "appointment">(
    "classes"
  );
  const [selectedDate, setSelectedDate] = useState(new Date());

  const { data, isLoading } = useClassesQuery({ date: selectedDate });

  console.log(data);

  // Create skeleton data for loading state
  const skeletonData = Array.from({ length: 5 }, (_, index) => ({ id: index }));

  return (
    <SafeAreaView className="flex-1 bg-white">
      <VStack className="flex-1">
        <ClassesHeader />
        <ClassesTabs selectedTab={selectedTab} onTabSelect={setSelectedTab} />
        <VStack space="md" className="pb-6">
          <HorizontalDatePicker
            selectedDate={selectedDate}
            onDateSelect={setSelectedDate}
          />
          <SearchInput />

          <VStack space="sm" className="px-4">
            {isLoading ? (
              // Show skeleton loading state
              <FlatList
                data={skeletonData}
                renderItem={() => <ClassCardSkeleton />}
                keyExtractor={(item) => String(item.id)}
                showsVerticalScrollIndicator={false}
              />
            ) : (
              // Show actual data
              <FlatList
                data={data}
                renderItem={({ item }) => <ClassCard key={item.id} {...item} />}
                keyExtractor={(item) => String(item.id)}
                showsVerticalScrollIndicator={false}
              />
            )}
          </VStack>
        </VStack>
      </VStack>
    </SafeAreaView>
  );
};

export default Classes;
