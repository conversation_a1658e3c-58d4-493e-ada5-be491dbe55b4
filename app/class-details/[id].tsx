import React, { useEffect, useState } from "react";
import { useLocalSearchParams, router } from "expo-router";
import { SafeAreaView } from "react-native-safe-area-context";
import { VStack } from "@/components/ui/vstack";
import { HStack } from "@/components/ui/hstack";
import { Text } from "@/components/ui/text";
import { Pressable } from "@/components/ui/pressable";
import { Icon } from "@/components/ui/icon";
import { ArrowLeft } from "lucide-react-native";
import ClassDetails from "@/components/screens/classes/class-details";
import { ClassDetailsResponse } from "@/data/screens/location/types";

const ClassDetailsScreen = () => {
  const { id } = useLocalSearchParams<{ id: string }>();
  const [classItem, setClassItem] = useState<ClassDetailsResponse | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // In a real app, this would fetch data from an API
    // For now, we'll create mock data that matches the design
    const mockClassData: ClassDetailsResponse = {
      id: Number(id),
      name: "Stretch & recover",
      description:
        "A form of cardio exercise alternating bouts if intense exercise with less intense recovery periods",
      start_time: "2025-04-17T17:30:00",
      end_time: "2025-04-17T18:15:00",
      instructor_first_name: "Gideon",
      instructor_last_name: "Ogunkola",
      gym_name: "Ann & Steve Hinchliffe San Pedro",
      room_name: "Room 230",
      spots_available: 15,
      spots: 30,
      class_type: "Live",
      // Other required fields with default values
      gym_id: 1,
      room_id: 1,
      allow_reservation_date: "",
      advance_time: 0,
      walkin_spots: 0,
      is_sgt: 0,
      class_category_id: 0,
      tags: [],
      days_of_week: [],
      allow_waitlist: 0,
      waitlist_spots: 0,
      slot_id: 0,
      category: "Low",
      instructor_id: 0,
      facility_closed: false,
      cancelled: false,
      allow_reservations: true,
      instructor: "Gideon Ogunkola",
      is_class_subbed: false,
      reservation_count: 0,
      waitlist_count: 0,
      waitlist_spots_available: 0,
      occupancy_total: 0,
      images: [],
    };

    setClassItem(mockClassData);
    setLoading(false);
  }, [id]);

  if (loading) {
    return (
      <SafeAreaView className="flex-1 bg-white">
        <VStack className="flex-1 justify-center items-center px-4">
          <Text className="text-lg font-dm-sans-medium text-typography-600">
            Loading class details...
          </Text>
        </VStack>
      </SafeAreaView>
    );
  }

  if (!classItem) {
    return (
      <SafeAreaView className="flex-1 bg-white">
        <VStack className="flex-1 justify-center items-center px-4">
          <Text className="text-lg font-dm-sans-bold text-typography-900 mb-2">
            Class Not Found
          </Text>
          <Text className="text-sm font-dm-sans-regular text-typography-600 text-center mb-6">
            The class you're looking for doesn't exist or has been removed.
          </Text>
          <Pressable
            onPress={() => router.back()}
            className="bg-primary-500 px-6 py-3 rounded-lg"
          >
            <Text className="text-white font-dm-sans-medium">Go Back</Text>
          </Pressable>
        </VStack>
      </SafeAreaView>
    );
  }

  return <ClassDetails classItem={classItem} />;
};

export default ClassDetailsScreen;
