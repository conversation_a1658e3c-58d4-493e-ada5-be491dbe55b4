import React from "react";
import { HStack } from "@/components/ui/hstack";
import { VStack } from "@/components/ui/vstack";
import { Text } from "@/components/ui/text";

import { Box } from "@/components/ui/box";
import { Heart } from "lucide-react-native";
import { Icon } from "@/components/ui/icon";
import { router } from "expo-router";
import { TouchableOpacity } from "react-native";
import { StatusButton } from "./class-button";
import { ClassDetailsResponse } from "@/data/screens/location/types";
import { getInitials, obtainDateFrame } from "@/data/common/common.utils";
import { Skeleton, SkeletonText } from "@/components/ui/skeleton";

const obtainSpotsAvailable = (
  type: "Virtual" | "Live" | "Virtual & Live",
  classData?: ClassDetailsResponse
) => {
  switch (type) {
    case "Virtual":
      return classData?.virtual_spots_available;
    case "Live":
      return classData?.spots_available;
    case "Virtual & Live":
      return (
        (classData?.virtual_spots_available ?? 0) +
        (classData?.spots_available ?? 0)
      );
    default:
      return 0;
  }
};

const obtainStatus = (type: "Virtual" | "Live" | "Virtual & Live") => {
  switch (type) {
    case "Virtual":
      return {
        classes: "bg-[#FCF4E7]",
        text: "Virtual",
      };
    case "Live":
      return {
        classes: "",
        text: "",
      };
    case "Virtual & Live":
      return {
        classes: "",
        text: "",
      };
    default:
      return {
        classes: "",
        text: "",
      };
  }
};
const ClassCard = (data: ClassDetailsResponse) => {
  const {
    name,
    class_type,
    start_time,
    end_time,
    instructor_first_name,
    instructor_last_name,
    gym_name,
    room_name,
    id,
  } = data;

  const handleCardPress = () => {
    try {
      router.push(`/class-details/${id}`);
    } catch (error) {
      console.error("Navigation error:", error);
    }
  };

  return (
    <TouchableOpacity onPress={handleCardPress} activeOpacity={0.7}>
      <VStack
        space="sm"
        className="bg-white rounded-2xl pl-2 pr-2 pt-3 pb-3  border border-background-200 mb-4"
      >
        <HStack className="justify-between items-start">
          <HStack space="sm" className="flex-1">
            <Box className="w-14 h-14 bg-background-200 rounded-lg items-center justify-center">
              <Text className="text-sm font-dm-sans-bold text-typography-900">
                {getInitials(name)}
              </Text>
            </Box>

            <VStack className="flex-1 flex gap-2" space="xs">
              <HStack className="items-center" space="xs">
                <Text className="text-[#00697B] font-dm-sans-bold text-base line-clamp-1 max-w-48">
                  {name}
                </Text>
                <Box
                  className={`p-2 rounded-full   ${
                    obtainStatus(class_type).classes
                  }`}
                >
                  <Text
                    className={`text-xs  font-bold ${
                      obtainStatus(class_type).classes
                    }`}
                  >
                    {obtainStatus(class_type).text}
                  </Text>
                </Box>
                <TouchableOpacity
                  className="ml-auto"
                  onPress={(e) => {
                    e.stopPropagation();
                  }}
                >
                  <Icon
                    as={Heart}
                    size="lg"
                    className={
                      false // fix later
                        ? "text-error-500 fill-error-500"
                        : "text-typography-400"
                    }
                  />
                </TouchableOpacity>
              </HStack>

              <HStack className="items-center" space="md">
                <Text className={`text-sm font-dm-sans-medium`}>
                  {obtainDateFrame(start_time, end_time)}
                </Text>
                <Box className="w-1 h-1 bg-[#00697B] rounded-full items-center justify-center" />
                <HStack className="items-center" space="4xl">
                  <Text className="text-sm font-dm-sans-regular text-typography-400">
                    {`${obtainSpotsAvailable(class_type, data)} spots left`}
                  </Text>
                </HStack>
              </HStack>
            </VStack>
          </HStack>
        </HStack>

        <HStack className="justify-between items-center mt-2 flex flex-row">
          <VStack space="xs" className="flex gap-1">
            <Text className="text-sm font-dm-sans-regular text-typography-600">
              {instructor_first_name} {instructor_last_name}
            </Text>
            <Text className="text-xs font-dm-sans-regular text-typography-600">
              {`${gym_name}, ${room_name}`}
            </Text>
          </VStack>
          <StatusButton status={"available"} />
        </HStack>
      </VStack>
    </TouchableOpacity>
  );
};

// ClassCard Loading Skeleton Component
export const ClassCardSkeleton = () => {
  return (
    <VStack
      space="sm"
      className="bg-white rounded-2xl pl-2 pr-2 pt-3 pb-3 border border-background-200 mb-4"
    >
      <HStack className="justify-between items-start">
        <HStack space="sm" className="flex-1">
          {/* Avatar skeleton */}
          <Skeleton className="w-14 h-14 rounded-lg" />

          <VStack className="flex-1 flex gap-2" space="xs">
            <HStack className="items-center" space="xs">
              {/* Class name skeleton */}
              <SkeletonText className="h-5 w-32 rounded" />

              {/* Virtual badge skeleton */}
              <Skeleton className="h-6 w-16 rounded-full" />

              {/* Heart icon skeleton */}
              <Skeleton className="ml-auto h-6 w-6 rounded" />
            </HStack>

            <HStack className="items-center" space="md">
              {/* Time skeleton */}
              <SkeletonText className="h-4 w-20 rounded" />

              {/* Dot separator */}
              <Skeleton className="w-1 h-1 rounded-full" />

              {/* Spots available skeleton */}
              <SkeletonText className="h-4 w-16 rounded" />
            </HStack>
          </VStack>
        </HStack>
      </HStack>

      <HStack className="justify-between items-center mt-2 flex flex-row">
        <VStack space="xs" className="flex gap-1">
          {/* Instructor name skeleton */}
          <SkeletonText className="h-4 w-24 rounded" />

          {/* Location skeleton */}
          <SkeletonText className="h-3 w-32 rounded" />
        </VStack>

        {/* Status button skeleton */}
        <Skeleton className="h-8 w-20 rounded-2xl" />
      </HStack>
    </VStack>
  );
};

export default ClassCard;
