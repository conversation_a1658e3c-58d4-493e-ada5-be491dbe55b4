import React, { useState } from "react";
import { VStack } from "@/components/ui/vstack";
import { HStack } from "@/components/ui/hstack";
import { Text } from "@/components/ui/text";
import { Button, ButtonText } from "@/components/ui/button";
import { ScrollView } from "@/components/ui/scroll-view";
import { SafeAreaView } from "react-native-safe-area-context";
import ClassCard, { ClassCardSkeleton } from "./index";
import { ClassDetailsResponse } from "@/data/screens/location/types";

// Demo component to showcase the ClassCardSkeleton
const ClassCardSkeletonDemo = () => {
  const [showSkeleton, setShowSkeleton] = useState(true);

  // Sample class data for comparison
  const sampleClassData: ClassDetailsResponse = {
    id: 1,
    name: "Morning Yoga Flow",
    class_type: "Virtual",
    start_time: "2024-01-15T09:00:00Z",
    end_time: "2024-01-15T10:00:00Z",
    instructor_first_name: "<PERSON>",
    instructor_last_name: "<PERSON>",
    gym_name: "Wellness Center",
    room_name: "Studio A",
    spots_available: 8,
    virtual_spots_available: 12,
    total_spots: 20,
    total_virtual_spots: 15,
    is_cancelled: false,
    is_closed: false,
    description: "A relaxing morning yoga session",
    instructor_id: 123,
    gym_id: 456,
    room_id: 789,
    university_id: "uni-123",
    created_at: "2024-01-01T00:00:00Z",
    updated_at: "2024-01-01T00:00:00Z"
  };

  return (
    <SafeAreaView className="flex-1 bg-background-50">
      <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
        <VStack space="lg" className="p-4">
          {/* Header */}
          <VStack space="sm">
            <Text className="text-2xl font-dm-sans-bold text-typography-900">
              ClassCard Skeleton Demo
            </Text>
            <Text className="text-sm font-dm-sans-regular text-typography-600">
              Toggle between loading skeleton and actual class card to see the comparison.
            </Text>
          </VStack>

          {/* Toggle Button */}
          <HStack className="justify-center">
            <Button
              onPress={() => setShowSkeleton(!showSkeleton)}
              className="bg-[#00BFE0] rounded-xl px-6"
            >
              <ButtonText className="text-white font-dm-sans-medium">
                {showSkeleton ? "Show Actual Card" : "Show Skeleton"}
              </ButtonText>
            </Button>
          </HStack>

          {/* Demo Section */}
          <VStack space="md">
            <Text className="text-lg font-dm-sans-bold text-typography-900">
              {showSkeleton ? "Loading State (Skeleton)" : "Loaded State (Actual Data)"}
            </Text>
            
            {showSkeleton ? (
              <ClassCardSkeleton />
            ) : (
              <ClassCard {...sampleClassData} />
            )}
          </VStack>

          {/* Multiple Skeletons Demo */}
          <VStack space="md">
            <Text className="text-lg font-dm-sans-bold text-typography-900">
              Multiple Loading Cards (List View)
            </Text>
            <Text className="text-sm font-dm-sans-regular text-typography-600">
              This is how it would look in a typical loading list:
            </Text>
            
            {Array.from({ length: 3 }, (_, index) => (
              <ClassCardSkeleton key={index} />
            ))}
          </VStack>

          {/* Usage Instructions */}
          <VStack space="md" className="bg-white p-4 rounded-xl border border-background-200">
            <Text className="text-lg font-dm-sans-bold text-typography-900">
              How to Use
            </Text>
            <VStack space="sm">
              <Text className="text-sm font-dm-sans-regular text-typography-700">
                1. Import both ClassCard and ClassCardSkeleton:
              </Text>
              <Text className="text-xs font-mono bg-background-100 p-2 rounded text-typography-800">
                {`import ClassCard, { ClassCardSkeleton } from "@/components/screens/classes/class-card";`}
              </Text>
              
              <Text className="text-sm font-dm-sans-regular text-typography-700">
                2. Use conditional rendering based on loading state:
              </Text>
              <Text className="text-xs font-mono bg-background-100 p-2 rounded text-typography-800">
                {`{isLoading ? <ClassCardSkeleton /> : <ClassCard {...data} />}`}
              </Text>
              
              <Text className="text-sm font-dm-sans-regular text-typography-700">
                3. For lists, create skeleton array:
              </Text>
              <Text className="text-xs font-mono bg-background-100 p-2 rounded text-typography-800">
                {`const skeletonData = Array.from({ length: 5 }, (_, i) => ({ id: i }));`}
              </Text>
            </VStack>
          </VStack>
        </VStack>
      </ScrollView>
    </SafeAreaView>
  );
};

export default ClassCardSkeletonDemo;
