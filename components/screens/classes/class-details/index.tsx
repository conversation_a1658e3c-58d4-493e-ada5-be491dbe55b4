import React, { useState } from "react";
import { ScrollView } from "@/components/ui/scroll-view";
import { VStack } from "@/components/ui/vstack";
import { HStack } from "@/components/ui/hstack";
import { Text } from "@/components/ui/text";
import { Button, ButtonText } from "@/components/ui/button";
import { Pressable } from "@/components/ui/pressable";
import { Box } from "@/components/ui/box";
import { Icon } from "@/components/ui/icon";
import {
  Heart,
  Clock,
  MapPin,
  Users,
  Calendar,
  Share,
  ArrowLeft,
} from "lucide-react-native";
import { ClassDetailsResponse } from "@/data/screens/location/types";
import {
  Image,
  ImageBackground,
  StyleSheet,
  View,
  Dimensions,
} from "react-native";
import { router } from "expo-router";

interface ClassDetailsProps {
  classItem: ClassDetailsResponse;
  isLoading?: boolean;
}

const ClassDetails: React.FC<ClassDetailsProps> = ({
  classItem,
  isLoading = false,
}) => {
  const [isFavorite, setIsFavorite] = useState(false);
  const windowHeight = Dimensions.get("window").height;

  if (isLoading) {
    return (
      <VStack className="flex-1 justify-center items-center px-4">
        <Text className="text-lg font-dm-sans-medium text-typography-600">
          Loading class details...
        </Text>
      </VStack>
    );
  }

  // Format date from ISO string
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const options: Intl.DateTimeFormatOptions = {
      weekday: "short",
      month: "long",
      day: "numeric",
      year: "numeric",
    };
    return date.toLocaleDateString("en-US", options);
  };

  // Format time from ISO string
  const formatTime = (startTime: string, endTime: string) => {
    const start = new Date(startTime);
    const end = new Date(endTime);

    const formatOptions: Intl.DateTimeFormatOptions = {
      hour: "numeric",
      minute: "numeric",
      hour12: true,
    };

    return `${start.toLocaleTimeString(
      "en-US",
      formatOptions
    )} - ${end.toLocaleTimeString("en-US", formatOptions)}`;
  };

  return (
    <View className="flex-1 bg-background-0">
      {/* Full-height background image section */}
      <View style={styles.imageContainer}>
        <ImageBackground
          source={{
            uri: "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=800&fit=crop&auto=format&q=80&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
          }}
          style={styles.backgroundImage}
          imageStyle={{
            opacity: 0.8, // Make image darker
            backgroundColor: "#000", // Black background
          }}
        >
          {/* Back button */}
          <Pressable
            onPress={() => router.back()}
            className="absolute top-12 left-4 p-2 bg-white/90 rounded-full z-10"
          >
            <Icon as={ArrowLeft} size="sm" className="text-typography-900" />
          </Pressable>

          {/* Share and favorite buttons */}
          <HStack className="absolute top-12 right-4 z-10" space="sm">
            <Pressable className="p-2 bg-white/90 rounded-full">
              <Icon as={Share} size="sm" className="text-typography-900" />
            </Pressable>
            <Pressable
              onPress={() => setIsFavorite(!isFavorite)}
              className="p-2 bg-white/90 rounded-full"
            >
              <Icon
                as={Heart}
                size="sm"
                className={
                  isFavorite
                    ? "text-error-500 fill-error-500"
                    : "text-typography-600"
                }
              />
            </Pressable>
          </HStack>

          {/* Title overlay at bottom of image section */}
          <Box className="absolute bottom-0 left-0 right-0 p-6 pb-8">
            <Text className="text-4xl font-dm-sans-bold text-white">
              {classItem.name}
            </Text>
          </Box>
        </ImageBackground>
      </View>

      {/* Content card that overlays the bottom portion of the image */}
      <Box className="bg-background-0 rounded-t-3xl -mt-5 flex-1">
        <ScrollView showsVerticalScrollIndicator={false} bounces={false}>
          <VStack space="lg" className="px-4 pt-6 pb-24">
            {/* Intensity and Duration Card */}
            <Box className="bg-white rounded-xl border border-background-200 p-4 mb-4">
              <HStack className="justify-between">
                {/* Intensity */}
                <VStack className="items-center">
                  <Box className="h-12 w-12 items-center justify-center">
                    <Box className="h-8 w-1 bg-success-500 rounded-full" />
                  </Box>
                  <Text className="text-base font-dm-sans-bold text-typography-900">
                    Low
                  </Text>
                  <Text className="text-xs font-dm-sans-regular text-typography-500">
                    INTENSITY
                  </Text>
                </VStack>

                {/* Divider */}
                <Box className="h-full w-px bg-background-200" />

                {/* Duration */}
                <VStack className="items-center">
                  <Box className="h-12 w-12 items-center justify-center">
                    <Icon as={Clock} size="lg" className="text-primary-500" />
                  </Box>
                  <Text className="text-base font-dm-sans-bold text-typography-900">
                    45 MINS
                  </Text>
                  <Text className="text-xs font-dm-sans-regular text-typography-500">
                    DURATION
                  </Text>
                </VStack>
              </HStack>
            </Box>

            {/* Date and Time */}
            <HStack className="items-center" space="xs">
              <Icon as={Calendar} size="sm" className="text-typography-600" />
              <Text className="text-sm font-dm-sans-medium text-typography-600">
                {formatDate(classItem.start_time)}
              </Text>
            </HStack>

            {/* Time */}
            <HStack className="items-center" space="xs">
              <Icon as={Clock} size="sm" className="text-typography-600" />
              <Text className="text-sm font-dm-sans-medium text-typography-600">
                {formatTime(classItem.start_time, classItem.end_time)}
              </Text>
            </HStack>

            {/* Slots */}
            <HStack className="items-center" space="xs">
              <Icon as={Users} size="sm" className="text-typography-600" />
              <Text className="text-sm font-dm-sans-medium text-typography-600">
                {classItem.spots_available} slots left
              </Text>
            </HStack>

            {/* Location */}
            <HStack className="items-center" space="xs">
              <Icon as={MapPin} size="sm" className="text-typography-600" />
              <Text className="text-sm font-dm-sans-medium text-typography-600">
                {classItem.gym_name}
              </Text>
            </HStack>

            {/* Room */}
            <HStack className="items-center pl-6">
              <Text className="text-sm font-dm-sans-medium text-typography-600">
                {classItem.room_name}
              </Text>
            </HStack>

            {/* Instructor Section */}
            <HStack
              space="sm"
              className="items-center border-t border-b border-background-200 py-4 mt-2"
            >
              <Box className="w-12 h-12 bg-primary-100 rounded-full items-center justify-center">
                <Text className="text-sm font-dm-sans-bold text-primary-500">
                  GO
                </Text>
              </Box>
              <VStack className="flex-1" space="xs">
                <Text className="text-xs font-dm-sans-regular text-typography-500">
                  Instructor
                </Text>
                <Text className="text-base font-dm-sans-bold text-typography-900">
                  {classItem.instructor_first_name}{" "}
                  {classItem.instructor_last_name}
                </Text>
              </VStack>
              <Pressable>
                <Text className="text-sm font-dm-sans-medium text-primary-500">
                  Read about
                </Text>
              </Pressable>
            </HStack>

            {/* Description */}
            <VStack space="sm">
              <Text className="text-lg font-dm-sans-bold text-typography-900">
                Description
              </Text>
              <Text className="text-sm font-dm-sans-regular text-typography-600 leading-5">
                {classItem.description}
              </Text>
            </VStack>
          </VStack>
        </ScrollView>

        {/* Fixed Bottom Action Bar */}
        <Box className="px-4 py-4 bg-white border-t border-background-200">
          <Button className="bg-primary-500 border-0 w-full">
            <ButtonText className="text-white font-dm-sans-medium">
              Reserve
            </ButtonText>
          </Button>
        </Box>
      </Box>
    </View>
  );
};

const styles = StyleSheet.create({
  imageContainer: {
    height: 330, // Fixed height like login page
  },
  backgroundImage: {
    width: "100%",
    height: "100%",
    justifyContent: "center",
  },
});

export default ClassDetails;
